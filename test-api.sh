#!/bin/bash

# Weight Tracker API Test Script
# This script tests the main API endpoints

BASE_URL="http://localhost:3001"
AUTH="admin:weight_tracker_2024"

echo "🧪 Testing Weight Tracker API..."
echo "=================================="

# Test 1: Health check
echo "1. Testing health endpoint..."
response=$(curl -s "$BASE_URL/health")
if [[ $response == *"OK"* ]]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    exit 1
fi

# Test 2: Get config
echo "2. Testing config endpoint..."
response=$(curl -s -u "$AUTH" "$BASE_URL/api/config")
if [[ $response == *"weekly_goal"* ]]; then
    echo "✅ Config endpoint working"
else
    echo "❌ Config endpoint failed"
    exit 1
fi

# Test 3: Add weight entry
echo "3. Testing weight entry creation..."
response=$(curl -s -u "$AUTH" -X POST -H "Content-Type: application/json" \
    -d '{"weight": 75.0, "note": "Test entry"}' \
    "$BASE_URL/api/weight")
if [[ $response == *"Weight entry created successfully"* ]]; then
    echo "✅ Weight entry creation working"
else
    echo "❌ Weight entry creation failed"
    exit 1
fi

# Test 4: Get daily weights
echo "4. Testing daily weights endpoint..."
response=$(curl -s -u "$AUTH" "$BASE_URL/api/weights/daily")
if [[ $response == *"data"* ]]; then
    echo "✅ Daily weights endpoint working"
else
    echo "❌ Daily weights endpoint failed"
    exit 1
fi

# Test 5: Get weekly weights
echo "5. Testing weekly weights endpoint..."
response=$(curl -s -u "$AUTH" "$BASE_URL/api/weights/weekly")
if [[ $response == *"data"* ]]; then
    echo "✅ Weekly weights endpoint working"
else
    echo "❌ Weekly weights endpoint failed"
    exit 1
fi

# Test 6: Get deviation data
echo "6. Testing deviation endpoint..."
response=$(curl -s -u "$AUTH" "$BASE_URL/api/weights/deviation")
if [[ $response == *"data"* ]]; then
    echo "✅ Deviation endpoint working"
else
    echo "❌ Deviation endpoint failed"
    exit 1
fi

# Test 7: Frontend accessibility
echo "7. Testing frontend accessibility..."
response=$(curl -s "http://localhost:5173" | head -1)
if [[ $response == *"<!doctype html>"* ]]; then
    echo "✅ Frontend accessible"
else
    echo "❌ Frontend not accessible"
    exit 1
fi

echo ""
echo "🎉 All tests passed! Weight Tracker is working correctly."
echo ""
echo "📱 Access the app at: http://localhost:5173"
echo "🔐 Default credentials: admin / weight_tracker_2024"
echo ""
echo "🚀 Ready to track your weight!"
