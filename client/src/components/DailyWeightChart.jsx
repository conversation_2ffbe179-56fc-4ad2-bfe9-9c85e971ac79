import { useState, useEffect } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { format, parseISO } from 'date-fns'
import { useAuth } from '../context/AuthContext'
import { useResponsive } from '../hooks/useResponsive'
import LoadingSpinner from './shared/LoadingSpinner'
import ErrorMessage from './shared/ErrorMessage'
import EmptyState from './shared/EmptyState'

export default function DailyWeightChart({ refreshTrigger }) {
  const [data, setData] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const { getAuthHeaders } = useAuth()
  const { isMobile } = useResponsive()

  useEffect(() => {
    fetchDailyWeights()
  }, [refreshTrigger])

  const fetchDailyWeights = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/weights/daily?limit=30', {
        headers: getAuthHeaders()
      })

      if (response.ok) {
        const result = await response.json()
        
        // Process data for chart
        const chartData = result.data
          .map(entry => ({
            date: entry.created_at.split(' ')[0], // Extract date part
            weight: entry.weight,
            note: entry.note,
            fullDate: entry.created_at
          }))
          .reverse() // Show oldest first for proper line progression

        setData(chartData)
        setError('')
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch daily weights')
      }
    } catch (error) {
      console.error('Error fetching daily weights:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">
            {format(parseISO(data.date), 'MMM dd, yyyy')}
          </p>
          <p className="text-primary-600">
            Weight: <span className="font-semibold">{data.weight} kg</span>
          </p>
          {data.note && (
            <p className="text-gray-600 text-sm mt-1">
              Note: {data.note}
            </p>
          )}
        </div>
      )
    }
    return null
  }

  const formatXAxisLabel = (tickItem) => {
    try {
      return format(parseISO(tickItem), 'MMM dd')
    } catch {
      return tickItem
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner size="md" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <ErrorMessage message={error} onRetry={fetchDailyWeights} />
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <EmptyState
          icon="📈"
          title="No weight entries yet"
          description="Add your first weight entry to see the chart"
        />
      </div>
    )
  }

  // Calculate weight range for better Y-axis scaling
  const weights = data.map(d => d.weight)
  const minWeight = Math.min(...weights)
  const maxWeight = Math.max(...weights)
  const range = maxWeight - minWeight
  const padding = Math.max(range * 0.1, 1) // At least 1kg padding
  
  return (
    <div className="w-full h-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: isMobile ? 10 : 30,
            left: isMobile ? 10 : 20,
            bottom: 5
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={formatXAxisLabel}
            stroke="#6b7280"
            fontSize={isMobile ? 10 : 12}
            interval={isMobile ? 'preserveStartEnd' : 0}
          />
          <YAxis
            domain={[minWeight - padding, maxWeight + padding]}
            stroke="#6b7280"
            fontSize={isMobile ? 10 : 12}
            tickFormatter={(value) => `${value.toFixed(1)}kg`}
            width={isMobile ? 40 : 60}
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey="weight"
            stroke="#0ea5e9"
            strokeWidth={2}
            dot={{ fill: '#0ea5e9', strokeWidth: 2, r: isMobile ? 3 : 4 }}
            activeDot={{ r: isMobile ? 5 : 6, stroke: '#0ea5e9', strokeWidth: 2, fill: '#fff' }}
          />
        </LineChart>
      </ResponsiveContainer>

      <div className="mt-2 sm:mt-4 text-center text-xs sm:text-sm text-gray-600">
        <div className="flex flex-col sm:flex-row sm:justify-center sm:items-center space-y-1 sm:space-y-0">
          <span>Showing last {data.length} entries</span>
          {data.length > 0 && (
            <span className="sm:ml-2">
              <span className="hidden sm:inline">•</span> Latest: {data[data.length - 1].weight} kg
            </span>
          )}
        </div>
      </div>
    </div>
  )
}
