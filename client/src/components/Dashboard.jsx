import { useState, useEffect } from 'react'
import { useAuth } from '../context/AuthContext'
import WeightEntryForm from './WeightEntryForm'
import DailyWeightChart from './DailyWeightChart'
import WeeklySummaryChart from './WeeklySummaryChart'
import WeeklyDeviationChart from './WeeklyDeviationChart'
import ConfigPanel from './ConfigPanel'

export default function Dashboard() {
  const { logout } = useAuth()
  const [activeTab, setActiveTab] = useState('dashboard')
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const triggerRefresh = () => {
    setRefreshTrigger(prev => prev + 1)
  }

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'entry', name: 'Add Weight', icon: '➕' },
    { id: 'config', name: 'Settings', icon: '⚙️' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">Weight Tracker</h1>
            </div>
            <button
              onClick={logout}
              className="text-gray-500 hover:text-gray-700 text-sm font-medium"
            >
              Sign out
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-2 sm:space-x-8 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-2 sm:px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span className="mr-1 sm:mr-2">{tab.icon}</span>
                <span className="hidden sm:inline">{tab.name}</span>
                <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {activeTab === 'dashboard' && (
          <div className="space-y-6 sm:space-y-8">
            <div className="grid grid-cols-1 gap-6 sm:gap-8">
              {/* Daily Weight Chart */}
              <div className="card">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Daily Weight Progress</h2>
                <div className="h-48 sm:h-64">
                  <DailyWeightChart refreshTrigger={refreshTrigger} />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 sm:gap-8">
              {/* Weekly Summary Chart */}
              <div className="card">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Weekly Summary</h2>
                <div className="h-48 sm:h-64">
                  <WeeklySummaryChart refreshTrigger={refreshTrigger} />
                </div>
              </div>

              {/* Weekly Deviation Chart */}
              <div className="card">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Goal Progress</h2>
                <div className="h-48 sm:h-64">
                  <WeeklyDeviationChart refreshTrigger={refreshTrigger} />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'entry' && (
          <div className="max-w-md mx-auto">
            <div className="card">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Add Weight Entry</h2>
              <WeightEntryForm onSuccess={triggerRefresh} />
            </div>
          </div>
        )}

        {activeTab === 'config' && (
          <div className="max-w-2xl mx-auto">
            <div className="card">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Settings</h2>
              <ConfigPanel />
            </div>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="text-center text-sm text-gray-500">
            <p>Weight Tracker • Track your progress, achieve your goals</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
