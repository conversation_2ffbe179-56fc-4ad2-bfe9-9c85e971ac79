import { useState, useEffect } from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts'
import { format, parseISO } from 'date-fns'
import { useAuth } from '../context/AuthContext'

export default function WeeklyDeviationChart({ refreshTrigger }) {
  const [data, setData] = useState([])
  const [config, setConfig] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const { getAuthHeaders } = useAuth()

  useEffect(() => {
    fetchDeviationData()
  }, [refreshTrigger])

  const fetchDeviationData = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/weights/deviation', {
        headers: getAuthHeaders()
      })

      if (response.ok) {
        const result = await response.json()
        
        // Process data for chart
        const chartData = result.data.map(week => ({
          week: week.week,
          weekStart: week.week_start,
          weekEnd: week.week_end,
          actualMedian: week.actual_median,
          expectedWeight: week.expected_weight,
          deviation: week.deviation,
          weeksFromStart: week.weeks_from_start,
          displayWeek: format(parseISO(week.week_start), 'MMM dd'),
          // Color coding: positive deviation = good (green), negative = bad (red)
          fill: week.deviation >= 0 ? '#22c55e' : '#ef4444'
        }))

        setData(chartData)
        setConfig(result.config)
        setError('')
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch deviation data')
      }
    } catch (error) {
      console.error('Error fetching deviation data:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      const isGood = data.deviation >= 0
      
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">
            Week of {format(parseISO(data.weekStart), 'MMM dd, yyyy')}
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-gray-600">
              Expected: <span className="font-semibold">{data.expectedWeight.toFixed(1)} kg</span>
            </p>
            <p className="text-gray-600">
              Actual: <span className="font-semibold">{data.actualMedian.toFixed(1)} kg</span>
            </p>
            <p className={isGood ? 'text-green-600' : 'text-red-600'}>
              Deviation: <span className="font-semibold">
                {data.deviation >= 0 ? '+' : ''}{data.deviation.toFixed(1)} kg
              </span>
            </p>
            <p className="text-xs text-gray-500 mt-2">
              {isGood 
                ? 'Better than expected! 🎉' 
                : 'Behind target, keep going! 💪'
              }
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  const CustomBar = (props) => {
    const { fill, ...rest } = props
    return <Bar {...rest} fill={props.payload.fill} />
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-2">{error}</p>
          {error.includes('Start week not configured') ? (
            <p className="text-sm text-gray-600 mb-3">
              Please set your start week in Settings to see goal progress.
            </p>
          ) : (
            <button
              onClick={fetchDeviationData}
              className="btn-secondary text-sm"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <p className="text-lg mb-2">No deviation data yet</p>
          <p className="text-sm">Set your start week in Settings to track progress</p>
        </div>
      </div>
    )
  }

  // Calculate Y-axis range
  const deviations = data.map(d => d.deviation)
  const maxDeviation = Math.max(...deviations.map(Math.abs))
  const yAxisRange = Math.max(maxDeviation * 1.2, 1) // At least 1kg range

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="displayWeek" 
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            domain={[-yAxisRange, yAxisRange]}
            stroke="#6b7280"
            fontSize={12}
            tickFormatter={(value) => `${value >= 0 ? '+' : ''}${value.toFixed(1)}kg`}
          />
          <Tooltip content={<CustomTooltip />} />
          <ReferenceLine y={0} stroke="#6b7280" strokeWidth={1} />
          
          <Bar 
            dataKey="deviation" 
            shape={<CustomBar />}
            radius={[2, 2, 2, 2]}
          />
        </BarChart>
      </ResponsiveContainer>
      
      <div className="mt-4 text-center">
        <div className="flex justify-center space-x-6 text-xs text-gray-600 mb-2">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 mr-1 rounded"></div>
            <span>Better than expected</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 mr-1 rounded"></div>
            <span>Behind target</span>
          </div>
        </div>
        
        {config && (
          <div className="text-xs text-gray-500">
            Goal: {config.weekly_goal} kg/week • Threshold: ±{config.threshold} kg
            {config.baseline_weight && (
              <span> • Starting weight: {config.baseline_weight.toFixed(1)} kg</span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
