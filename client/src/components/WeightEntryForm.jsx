import { useState } from 'react'
import { useAuth } from '../context/AuthContext'

export default function WeightEntryForm({ onSuccess }) {
  const [weight, setWeight] = useState('')
  const [note, setNote] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const { getAuthHeaders } = useAuth()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')
    setSuccess('')

    // Validation
    const weightNum = parseFloat(weight)
    if (!weight || isNaN(weightNum) || weightNum <= 0 || weightNum >= 1000) {
      setError('Please enter a valid weight between 0 and 1000 kg')
      setIsLoading(false)
      return
    }

    if (note && note.length > 100) {
      setError('Note must be 100 characters or less')
      setIsLoading(false)
      return
    }

    try {
      const response = await fetch('/api/weight', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({
          weight: weightNum,
          note: note.trim() || undefined
        })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess('Weight entry added successfully!')
        setWeight('')
        setNote('')
        if (onSuccess) {
          onSuccess()
        }
      } else {
        setError(data.error || 'Failed to add weight entry')
      }
    } catch (error) {
      console.error('Error adding weight entry:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="weight" className="block text-sm font-medium text-gray-700 mb-1">
          Weight (kg) *
        </label>
        <input
          type="number"
          id="weight"
          step="0.1"
          min="0"
          max="999"
          className="input-field"
          placeholder="e.g., 75.5"
          value={weight}
          onChange={(e) => setWeight(e.target.value)}
          disabled={isLoading}
          required
        />
      </div>

      <div>
        <label htmlFor="note" className="block text-sm font-medium text-gray-700 mb-1">
          Note (optional)
        </label>
        <input
          type="text"
          id="note"
          maxLength="100"
          className="input-field"
          placeholder="e.g., Morning weight, after workout..."
          value={note}
          onChange={(e) => setNote(e.target.value)}
          disabled={isLoading}
        />
        <p className="text-xs text-gray-500 mt-1">
          {note.length}/100 characters
        </p>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 p-3">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {success && (
        <div className="rounded-md bg-green-50 p-3">
          <div className="text-sm text-green-700">{success}</div>
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="btn-primary w-full"
      >
        {isLoading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Adding...
          </div>
        ) : (
          'Add Weight Entry'
        )}
      </button>
    </form>
  )
}
