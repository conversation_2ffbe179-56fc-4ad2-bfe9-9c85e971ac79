import { useState, useEffect } from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts'
import { format, parseISO } from 'date-fns'
import { useAuth } from '../context/AuthContext'

export default function WeeklySummaryChart({ refreshTrigger }) {
  const [data, setData] = useState([])
  const [config, setConfig] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const { getAuthHeaders } = useAuth()

  useEffect(() => {
    Promise.all([fetchWeeklyWeights(), fetchConfig()])
  }, [refreshTrigger])

  const fetchWeeklyWeights = async () => {
    try {
      const response = await fetch('/api/weights/weekly', {
        headers: getAuthHeaders()
      })

      if (response.ok) {
        const result = await response.json()
        
        // Process data for chart
        const chartData = result.data
          .map(week => ({
            week: week.week,
            weekStart: week.week_start,
            weekEnd: week.week_end,
            minWeight: week.min_weight,
            maxWeight: week.max_weight,
            avgWeight: week.avg_weight,
            medianWeight: week.median_weight,
            entryCount: week.entry_count,
            displayWeek: format(parseISO(week.week_start), 'MMM dd')
          }))
          .reverse() // Show oldest first

        setData(chartData)
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch weekly weights')
      }
    } catch (error) {
      console.error('Error fetching weekly weights:', error)
      setError('Network error. Please try again.')
    }
  }

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/config', {
        headers: getAuthHeaders()
      })

      if (response.ok) {
        const result = await response.json()
        setConfig(result.data)
      }
    } catch (error) {
      console.error('Error fetching config:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">
            Week of {format(parseISO(data.weekStart), 'MMM dd, yyyy')}
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-green-600">
              Min: <span className="font-semibold">{data.minWeight.toFixed(1)} kg</span>
            </p>
            <p className="text-blue-600">
              Median: <span className="font-semibold">{data.medianWeight.toFixed(1)} kg</span>
            </p>
            <p className="text-red-600">
              Max: <span className="font-semibold">{data.maxWeight.toFixed(1)} kg</span>
            </p>
            <p className="text-gray-600">
              Entries: {data.entryCount}
            </p>
          </div>
        </div>
      )
    }
    return null
  }

  // Calculate goal lines if config is available
  const calculateGoalLines = () => {
    if (!config || !config.start_week) {
      return data.length > 0 ? data : []
    }

    if (data.length === 0) {
      return []
    }

    // Find baseline weight (first week's median)
    const baseline = data[0].medianWeight

    // Calculate goal lines for each week
    const goalData = data.map((week, index) => {
      const expectedWeight = baseline - (index * config.weekly_goal)
      return {
        ...week,
        expectedWeight,
        upperGoal: expectedWeight + config.threshold,
        lowerGoal: expectedWeight - config.threshold
      }
    })

    return goalData
  }

  const goalData = calculateGoalLines()
  const hasGoalLines = config && config.start_week && goalData.length > 0

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-2">{error}</p>
          <button
            onClick={() => Promise.all([fetchWeeklyWeights(), fetchConfig()])}
            className="btn-secondary text-sm"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (data.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-gray-500">
          <p className="text-lg mb-2">No weekly data yet</p>
          <p className="text-sm">Add weight entries to see weekly summaries</p>
        </div>
      </div>
    )
  }

  // Calculate weight range for better Y-axis scaling
  const allWeights = data.flatMap(d => [d.minWeight, d.maxWeight, d.medianWeight])
  if (hasGoalLines) {
    allWeights.push(...goalData.flatMap(d => [d.upperGoal, d.lowerGoal]))
  }
  const minWeight = Math.min(...allWeights)
  const maxWeight = Math.max(...allWeights)
  const range = maxWeight - minWeight
  const padding = Math.max(range * 0.1, 1)

  const chartData = hasGoalLines ? goalData : data

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis 
            dataKey="displayWeek" 
            stroke="#6b7280"
            fontSize={12}
          />
          <YAxis 
            domain={[minWeight - padding, maxWeight + padding]}
            stroke="#6b7280"
            fontSize={12}
            tickFormatter={(value) => `${value.toFixed(1)}kg`}
          />
          <Tooltip content={<CustomTooltip />} />
          
          {/* Goal threshold lines */}
          {hasGoalLines && (
            <>
              <Line 
                type="monotone" 
                dataKey="upperGoal" 
                stroke="#d1d5db" 
                strokeWidth={1}
                strokeDasharray="5 5"
                dot={false}
                activeDot={false}
              />
              <Line 
                type="monotone" 
                dataKey="lowerGoal" 
                stroke="#d1d5db" 
                strokeWidth={1}
                strokeDasharray="5 5"
                dot={false}
                activeDot={false}
              />
            </>
          )}
          
          {/* Weight data lines */}
          <Line 
            type="monotone" 
            dataKey="minWeight" 
            stroke="#22c55e" 
            strokeWidth={2}
            dot={{ fill: '#22c55e', strokeWidth: 2, r: 3 }}
            name="Min Weight"
          />
          <Line 
            type="monotone" 
            dataKey="medianWeight" 
            stroke="#0ea5e9" 
            strokeWidth={3}
            dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: '#0ea5e9', strokeWidth: 2, fill: '#fff' }}
            name="Median Weight"
          />
          <Line 
            type="monotone" 
            dataKey="maxWeight" 
            stroke="#ef4444" 
            strokeWidth={2}
            dot={{ fill: '#ef4444', strokeWidth: 2, r: 3 }}
            name="Max Weight"
          />
        </LineChart>
      </ResponsiveContainer>
      
      <div className="mt-4 flex justify-center space-x-6 text-xs text-gray-600">
        <div className="flex items-center">
          <div className="w-3 h-0.5 bg-green-500 mr-1"></div>
          <span>Min</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-0.5 bg-blue-500 mr-1"></div>
          <span>Median</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-0.5 bg-red-500 mr-1"></div>
          <span>Max</span>
        </div>
        {hasGoalLines && (
          <div className="flex items-center">
            <div className="w-3 h-0.5 bg-gray-400 mr-1 border-dashed"></div>
            <span>Goal ±{config.threshold}kg</span>
          </div>
        )}
      </div>
      <div className="text-center text-xs text-gray-500 mt-2">
        Showing last 10 weeks • Monday-Sunday weeks
      </div>
    </div>
  )
}
