import { useState, useEffect } from 'react'
import { useAuth } from '../context/AuthContext'
import { format } from 'date-fns'

export default function ConfigPanel() {
  const [config, setConfig] = useState(null)
  const [weeklyGoal, setWeeklyGoal] = useState('')
  const [threshold, setThreshold] = useState('')
  const [startWeek, setStartWeek] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteConfirmText, setDeleteConfirmText] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const { getAuthHeaders } = useAuth()

  useEffect(() => {
    fetchConfig()
  }, [])

  const fetchConfig = async () => {
    try {
      const response = await fetch('/api/config', {
        headers: getAuthHeaders()
      })

      if (response.ok) {
        const data = await response.json()
        setConfig(data.data)
        setWeeklyGoal(data.data.weekly_goal.toString())
        setThreshold(data.data.threshold.toString())
        setStartWeek(data.data.start_week || '')
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to fetch configuration')
      }
    } catch (error) {
      console.error('Error fetching config:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSaving(true)
    setError('')
    setSuccess('')

    // Validation
    const goalNum = parseFloat(weeklyGoal)
    const thresholdNum = parseFloat(threshold)

    if (isNaN(goalNum) || goalNum < 0 || goalNum > 5) {
      setError('Weekly goal must be between 0 and 5 kg')
      setIsSaving(false)
      return
    }

    if (isNaN(thresholdNum) || thresholdNum < 0 || thresholdNum > 2) {
      setError('Threshold must be between 0 and 2 kg')
      setIsSaving(false)
      return
    }

    if (startWeek && !/^\d{4}-\d{2}-\d{2}$/.test(startWeek)) {
      setError('Start week must be in YYYY-MM-DD format')
      setIsSaving(false)
      return
    }

    try {
      const payload = {
        weekly_goal: goalNum,
        threshold: thresholdNum
      }

      if (startWeek) {
        payload.start_week = startWeek
      }

      const response = await fetch('/api/config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify(payload)
      })

      const data = await response.json()

      if (response.ok) {
        setConfig(data.data)
        setSuccess('Configuration updated successfully!')
      } else {
        setError(data.error || 'Failed to update configuration')
      }
    } catch (error) {
      console.error('Error updating config:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const setStartWeekToToday = () => {
    const today = format(new Date(), 'yyyy-MM-dd')
    setStartWeek(today)
  }

  const handleDeleteAllData = async () => {
    if (deleteConfirmText !== 'YES') {
      setError('Please type "YES" to confirm deletion')
      return
    }

    setIsDeleting(true)
    setError('')
    setSuccess('')

    try {
      const response = await fetch('/api/weights/all', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({ confirmation: 'YES' })
      })

      const data = await response.json()

      if (response.ok) {
        setSuccess(`All data deleted successfully! (${data.deleted_count} entries removed)`)
        setShowDeleteConfirm(false)
        setDeleteConfirmText('')
      } else {
        setError(data.error || 'Failed to delete data')
      }
    } catch (error) {
      console.error('Error deleting data:', error)
      setError('Network error. Please try again.')
    } finally {
      setIsDeleting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="weeklyGoal" className="block text-sm font-medium text-gray-700 mb-1">
            Weekly Weight Loss Goal (kg/week)
          </label>
          <input
            type="number"
            id="weeklyGoal"
            step="0.1"
            min="0"
            max="5"
            className="input-field"
            placeholder="e.g., 0.5"
            value={weeklyGoal}
            onChange={(e) => setWeeklyGoal(e.target.value)}
            disabled={isSaving}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            How much weight you want to lose per week (0-5 kg)
          </p>
        </div>

        <div>
          <label htmlFor="threshold" className="block text-sm font-medium text-gray-700 mb-1">
            Threshold (±kg)
          </label>
          <input
            type="number"
            id="threshold"
            step="0.1"
            min="0"
            max="2"
            className="input-field"
            placeholder="e.g., 0.3"
            value={threshold}
            onChange={(e) => setThreshold(e.target.value)}
            disabled={isSaving}
            required
          />
          <p className="text-xs text-gray-500 mt-1">
            Acceptable deviation from your weekly goal (0-2 kg)
          </p>
        </div>

        <div>
          <label htmlFor="startWeek" className="block text-sm font-medium text-gray-700 mb-1">
            Start Week (YYYY-MM-DD)
          </label>
          <div className="flex space-x-2">
            <input
              type="date"
              id="startWeek"
              className="input-field flex-1"
              value={startWeek}
              onChange={(e) => setStartWeek(e.target.value)}
              disabled={isSaving}
            />
            <button
              type="button"
              onClick={setStartWeekToToday}
              className="btn-secondary whitespace-nowrap"
              disabled={isSaving}
            >
              Today
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            The date when you started your weight loss plan
          </p>
        </div>

        {error && (
          <div className="rounded-md bg-red-50 p-3">
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {success && (
          <div className="rounded-md bg-green-50 p-3">
            <div className="text-sm text-green-700">{success}</div>
          </div>
        )}

        <button
          type="submit"
          disabled={isSaving}
          className="btn-primary w-full"
        >
          {isSaving ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </div>
          ) : (
            'Save Configuration'
          )}
        </button>
      </form>

      {config && (
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Current Configuration</h3>
          <dl className="text-sm text-gray-600 space-y-1">
            <div className="flex justify-between">
              <dt>Weekly Goal:</dt>
              <dd>{config.weekly_goal} kg/week</dd>
            </div>
            <div className="flex justify-between">
              <dt>Threshold:</dt>
              <dd>±{config.threshold} kg</dd>
            </div>
            <div className="flex justify-between">
              <dt>Start Week:</dt>
              <dd>{config.start_week || 'Not set'}</dd>
            </div>
            <div className="flex justify-between">
              <dt>Last Updated:</dt>
              <dd>{config.updated_at}</dd>
            </div>
          </dl>
        </div>
      )}

      {/* Danger Zone - Delete All Data */}
      <div className="border-t pt-6 mt-6">
        <h3 className="text-lg font-medium text-red-600 mb-4">⚠️ Danger Zone</h3>

        {!showDeleteConfirm ? (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">Delete All Weight Data</h4>
            <p className="text-sm text-red-700 mb-4">
              This will permanently delete all your weight entries. This action cannot be undone.
            </p>
            <button
              type="button"
              onClick={() => setShowDeleteConfirm(true)}
              className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
            >
              Delete All Data
            </button>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <h4 className="font-medium text-red-800 mb-2">⚠️ Confirm Deletion</h4>
            <p className="text-sm text-red-700 mb-4">
              This will permanently delete ALL your weight entries. Type <strong>YES</strong> to confirm:
            </p>

            <div className="space-y-4">
              <input
                type="text"
                placeholder="Type YES to confirm"
                value={deleteConfirmText}
                onChange={(e) => setDeleteConfirmText(e.target.value)}
                className="w-full px-3 py-2 border border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                disabled={isDeleting}
              />

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleDeleteAllData}
                  disabled={isDeleting || deleteConfirmText !== 'YES'}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-red-300 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:cursor-not-allowed"
                >
                  {isDeleting ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Deleting...
                    </div>
                  ) : (
                    'Delete All Data'
                  )}
                </button>

                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteConfirm(false)
                    setDeleteConfirmText('')
                    setError('')
                  }}
                  disabled={isDeleting}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
