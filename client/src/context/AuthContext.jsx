import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export function useAuth() {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [credentials, setCredentials] = useState(null)

  // Check for stored credentials on mount
  useEffect(() => {
    const storedCredentials = localStorage.getItem('weight_tracker_auth')
    if (storedCredentials) {
      try {
        const parsed = JSON.parse(storedCredentials)
        setCredentials(parsed)
        setIsAuthenticated(true)
      } catch (error) {
        console.error('Error parsing stored credentials:', error)
        localStorage.removeItem('weight_tracker_auth')
      }
    }
    setIsLoading(false)
  }, [])

  const login = async (username, password) => {
    try {
      // Test the credentials by making a request to the API
      const response = await fetch('/api/config', {
        headers: {
          'Authorization': `Basic ${btoa(`${username}:${password}`)}`
        }
      })

      if (response.ok) {
        const creds = { username, password }
        setCredentials(creds)
        setIsAuthenticated(true)
        localStorage.setItem('weight_tracker_auth', JSON.stringify(creds))
        return { success: true }
      } else {
        const error = await response.json()
        return { success: false, error: error.message || 'Authentication failed' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, error: 'Network error. Please check if the server is running.' }
    }
  }

  const logout = () => {
    setCredentials(null)
    setIsAuthenticated(false)
    localStorage.removeItem('weight_tracker_auth')
  }

  const getAuthHeaders = () => {
    if (!credentials) {
      throw new Error('Not authenticated')
    }
    return {
      'Authorization': `Basic ${btoa(`${credentials.username}:${credentials.password}`)}`
    }
  }

  const value = {
    isAuthenticated,
    isLoading,
    credentials,
    login,
    logout,
    getAuthHeaders
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
