const express = require('express');
const { runQuery, getQuery } = require('../database/init');
const router = express.Router();

/**
 * GET /api/config - Get current configuration
 */
router.get('/', async (req, res) => {
  try {
    const config = await getQuery(`
      SELECT weekly_goal, threshold, start_week,
             datetime(created_at, 'localtime') as created_at,
             datetime(updated_at, 'localtime') as updated_at
      FROM app_config 
      WHERE id = 1
    `);

    if (!config) {
      // This shouldn't happen due to default insert, but just in case
      return res.status(404).json({ error: 'Configuration not found' });
    }

    res.json({
      data: config
    });

  } catch (error) {
    console.error('Error fetching configuration:', error);
    res.status(500).json({ error: 'Failed to fetch configuration' });
  }
});

/**
 * POST /api/config - Update configuration
 */
router.post('/', async (req, res) => {
  try {
    const { weekly_goal, threshold, start_week } = req.body;

    // Validation
    const updates = {};
    const params = [];
    const setParts = [];

    if (weekly_goal !== undefined) {
      if (typeof weekly_goal !== 'number' || weekly_goal < 0 || weekly_goal > 5) {
        return res.status(400).json({ 
          error: 'Weekly goal must be a number between 0 and 5 kg' 
        });
      }
      setParts.push('weekly_goal = ?');
      params.push(weekly_goal);
      updates.weekly_goal = weekly_goal;
    }

    if (threshold !== undefined) {
      if (typeof threshold !== 'number' || threshold < 0 || threshold > 2) {
        return res.status(400).json({ 
          error: 'Threshold must be a number between 0 and 2 kg' 
        });
      }
      setParts.push('threshold = ?');
      params.push(threshold);
      updates.threshold = threshold;
    }

    if (start_week !== undefined) {
      if (typeof start_week !== 'string') {
        return res.status(400).json({ 
          error: 'Start week must be a date string (YYYY-MM-DD)' 
        });
      }
      
      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(start_week)) {
        return res.status(400).json({ 
          error: 'Start week must be in YYYY-MM-DD format' 
        });
      }

      const date = new Date(start_week);
      if (isNaN(date.getTime())) {
        return res.status(400).json({ 
          error: 'Invalid date for start week' 
        });
      }

      setParts.push('start_week = ?');
      params.push(start_week);
      updates.start_week = start_week;
    }

    if (setParts.length === 0) {
      return res.status(400).json({ 
        error: 'At least one field must be provided (weekly_goal, threshold, or start_week)' 
      });
    }

    // Add updated_at
    setParts.push('updated_at = CURRENT_TIMESTAMP');
    params.push(1); // WHERE id = 1

    const sql = `UPDATE app_config SET ${setParts.join(', ')} WHERE id = ?`;
    
    await runQuery(sql, params);

    // Get updated configuration
    const updatedConfig = await getQuery(`
      SELECT weekly_goal, threshold, start_week,
             datetime(created_at, 'localtime') as created_at,
             datetime(updated_at, 'localtime') as updated_at
      FROM app_config 
      WHERE id = 1
    `);

    res.json({
      message: 'Configuration updated successfully',
      data: updatedConfig,
      updated_fields: Object.keys(updates)
    });

  } catch (error) {
    console.error('Error updating configuration:', error);
    res.status(500).json({ error: 'Failed to update configuration' });
  }
});

/**
 * POST /api/config/start-week - Set/reset the start week (convenience endpoint)
 */
router.post('/start-week', async (req, res) => {
  try {
    const { start_week } = req.body;

    if (!start_week || typeof start_week !== 'string') {
      return res.status(400).json({ 
        error: 'Start week is required and must be a date string (YYYY-MM-DD)' 
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(start_week)) {
      return res.status(400).json({ 
        error: 'Start week must be in YYYY-MM-DD format' 
      });
    }

    const date = new Date(start_week);
    if (isNaN(date.getTime())) {
      return res.status(400).json({ 
        error: 'Invalid date for start week' 
      });
    }

    // Update start week
    await runQuery(
      'UPDATE app_config SET start_week = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1',
      [start_week]
    );

    // Get updated configuration
    const updatedConfig = await getQuery(`
      SELECT weekly_goal, threshold, start_week,
             datetime(created_at, 'localtime') as created_at,
             datetime(updated_at, 'localtime') as updated_at
      FROM app_config 
      WHERE id = 1
    `);

    res.json({
      message: 'Start week updated successfully',
      data: updatedConfig
    });

  } catch (error) {
    console.error('Error updating start week:', error);
    res.status(500).json({ error: 'Failed to update start week' });
  }
});

module.exports = router;
