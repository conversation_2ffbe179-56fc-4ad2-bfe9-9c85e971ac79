const express = require('express');
const { runQuery, getQuery, allQuery } = require('../database/init');
const router = express.Router();

/**
 * POST /api/weight - Add new weight entry
 */
router.post('/', async (req, res) => {
  try {
    const { weight, note } = req.body;

    // Validation
    if (!weight || typeof weight !== 'number') {
      return res.status(400).json({ 
        error: 'Weight is required and must be a number' 
      });
    }

    if (weight <= 0 || weight >= 1000) {
      return res.status(400).json({ 
        error: 'Weight must be between 0 and 1000 kg' 
      });
    }

    if (note && typeof note !== 'string') {
      return res.status(400).json({ 
        error: 'Note must be a string' 
      });
    }

    if (note && note.length > 100) {
      return res.status(400).json({ 
        error: 'Note must be 100 characters or less' 
      });
    }

    // Insert weight entry
    const result = await runQuery(
      'INSERT INTO weight_entries (weight, note) VALUES (?, ?)',
      [weight, note || null]
    );

    // Get the created entry
    const newEntry = await getQuery(
      'SELECT * FROM weight_entries WHERE id = ?',
      [result.id]
    );

    res.status(201).json({
      message: 'Weight entry created successfully',
      data: newEntry
    });

  } catch (error) {
    console.error('Error creating weight entry:', error);
    res.status(500).json({ error: 'Failed to create weight entry' });
  }
});

/**
 * GET /api/weights/daily - Get all daily weight entries
 */
router.get('/daily', async (req, res) => {
  try {
    const { limit, offset, startDate, endDate } = req.query;
    
    let sql = `
      SELECT id, weight, note, 
             datetime(created_at, 'localtime') as created_at,
             datetime(updated_at, 'localtime') as updated_at
      FROM weight_entries 
      WHERE 1=1
    `;
    const params = [];

    // Date filtering
    if (startDate) {
      sql += ' AND date(created_at) >= date(?)';
      params.push(startDate);
    }
    
    if (endDate) {
      sql += ' AND date(created_at) <= date(?)';
      params.push(endDate);
    }

    sql += ' ORDER BY created_at DESC';

    // Pagination
    if (limit) {
      const limitNum = parseInt(limit);
      if (limitNum > 0 && limitNum <= 1000) {
        sql += ' LIMIT ?';
        params.push(limitNum);
        
        if (offset) {
          const offsetNum = parseInt(offset);
          if (offsetNum >= 0) {
            sql += ' OFFSET ?';
            params.push(offsetNum);
          }
        }
      }
    }

    const entries = await allQuery(sql, params);
    
    res.json({
      data: entries,
      count: entries.length
    });

  } catch (error) {
    console.error('Error fetching daily weights:', error);
    res.status(500).json({ error: 'Failed to fetch daily weights' });
  }
});

/**
 * GET /api/weights/weekly - Get weekly weight summaries (Monday-Sunday weeks)
 */
router.get('/weekly', async (req, res) => {
  try {
    const sql = `
      SELECT
        strftime('%Y-%W', date(created_at, 'weekday 1', '-6 days')) as week,
        date(created_at, 'weekday 1', '-6 days') as week_start,
        date(created_at, 'weekday 1') as week_end,
        MIN(weight) as min_weight,
        MAX(weight) as max_weight,
        AVG(weight) as avg_weight,
        COUNT(*) as entry_count,
        GROUP_CONCAT(weight ORDER BY created_at) as weights
      FROM weight_entries
      GROUP BY strftime('%Y-%W', date(created_at, 'weekday 1', '-6 days'))
      ORDER BY week DESC
      LIMIT 10
    `;

    const weeklyData = await allQuery(sql);
    
    // Calculate median for each week
    const processedData = weeklyData.map(week => {
      const weights = week.weights.split(',').map(w => parseFloat(w)).sort((a, b) => a - b);
      const median = weights.length % 2 === 0
        ? (weights[weights.length / 2 - 1] + weights[weights.length / 2]) / 2
        : weights[Math.floor(weights.length / 2)];
      
      return {
        week: week.week,
        week_start: week.week_start,
        week_end: week.week_end,
        min_weight: parseFloat(week.min_weight),
        max_weight: parseFloat(week.max_weight),
        avg_weight: parseFloat(week.avg_weight),
        median_weight: median,
        entry_count: week.entry_count
      };
    });

    res.json({
      data: processedData,
      count: processedData.length
    });

  } catch (error) {
    console.error('Error fetching weekly weights:', error);
    res.status(500).json({ error: 'Failed to fetch weekly weights' });
  }
});

/**
 * GET /api/weights/deviation - Get weekly deviation from goals
 */
router.get('/deviation', async (req, res) => {
  try {
    // Get configuration
    const config = await getQuery('SELECT * FROM app_config WHERE id = 1');
    
    if (!config || !config.start_week) {
      return res.status(400).json({ 
        error: 'Start week not configured. Please set up your weight loss plan first.' 
      });
    }

    // Get weekly data (Monday-Sunday weeks)
    const weeklyData = await allQuery(`
      SELECT
        strftime('%Y-%W', date(created_at, 'weekday 1', '-6 days')) as week,
        date(created_at, 'weekday 1', '-6 days') as week_start,
        date(created_at, 'weekday 1') as week_end,
        GROUP_CONCAT(weight ORDER BY created_at) as weights
      FROM weight_entries
      WHERE date(created_at) >= date(?)
      GROUP BY strftime('%Y-%W', date(created_at, 'weekday 1', '-6 days'))
      ORDER BY week ASC
      LIMIT 10
    `, [config.start_week]);

    // Calculate deviations
    const deviations = [];
    const startWeekDate = new Date(config.start_week);
    
    // Get baseline weight (first entry of start week or closest)
    const baselineEntry = await getQuery(`
      SELECT weight FROM weight_entries 
      WHERE date(created_at) >= date(?)
      ORDER BY created_at ASC 
      LIMIT 1
    `, [config.start_week]);

    if (!baselineEntry) {
      return res.json({ data: [], count: 0 });
    }

    const baselineWeight = baselineEntry.weight;

    weeklyData.forEach((week, index) => {
      const weights = week.weights.split(',').map(w => parseFloat(w)).sort((a, b) => a - b);
      const median = weights.length % 2 === 0
        ? (weights[weights.length / 2 - 1] + weights[weights.length / 2]) / 2
        : weights[Math.floor(weights.length / 2)];

      // Calculate expected weight for this week
      const weeksFromStart = index;
      const expectedWeight = baselineWeight - (weeksFromStart * config.weekly_goal);
      
      // Calculate deviation (positive = better than expected, negative = worse)
      const deviation = expectedWeight - median;

      deviations.push({
        week: week.week,
        week_start: week.week_start,
        week_end: week.week_end,
        actual_median: median,
        expected_weight: expectedWeight,
        deviation: deviation,
        weeks_from_start: weeksFromStart
      });
    });

    res.json({
      data: deviations,
      count: deviations.length,
      config: {
        weekly_goal: config.weekly_goal,
        threshold: config.threshold,
        start_week: config.start_week,
        baseline_weight: baselineWeight
      }
    });

  } catch (error) {
    console.error('Error calculating weight deviations:', error);
    res.status(500).json({ error: 'Failed to calculate weight deviations' });
  }
});

/**
 * DELETE /api/weights/all - Delete all weight entries
 */
router.delete('/all', async (req, res) => {
  try {
    const { confirmation } = req.body;

    // Require explicit confirmation
    if (confirmation !== 'YES') {
      return res.status(400).json({
        error: 'Confirmation required. Send {"confirmation": "YES"} to delete all data.'
      });
    }

    // Delete all weight entries
    const result = await runQuery('DELETE FROM weight_entries');

    res.json({
      message: 'All weight entries deleted successfully',
      deleted_count: result.changes
    });

  } catch (error) {
    console.error('Error deleting all weight entries:', error);
    res.status(500).json({ error: 'Failed to delete weight entries' });
  }
});

module.exports = router;
