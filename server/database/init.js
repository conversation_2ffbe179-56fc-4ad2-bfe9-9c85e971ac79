const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;

let db = null;

/**
 * Get database connection
 */
function getDatabase() {
  if (!db) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return db;
}

/**
 * Initialize SQLite database with required tables
 */
async function initializeDatabase() {
  try {
    const dbPath = process.env.DB_PATH || './data/weight_tracker.db';
    const dbDir = path.dirname(dbPath);
    
    // Ensure data directory exists
    try {
      await fs.access(dbDir);
    } catch {
      await fs.mkdir(dbDir, { recursive: true });
      console.log(`Created database directory: ${dbDir}`);
    }

    // Create database connection
    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        console.error('Error opening database:', err);
        throw err;
      }
      console.log(`Connected to SQLite database: ${dbPath}`);
    });

    // Enable foreign keys
    await runQuery('PRAGMA foreign_keys = ON');

    // Create tables
    await createTables();
    
    console.log('Database initialization completed');
    return db;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
}

/**
 * Create database tables
 */
async function createTables() {
  // Weight entries table
  await runQuery(`
    CREATE TABLE IF NOT EXISTS weight_entries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      weight REAL NOT NULL CHECK(weight > 0 AND weight < 1000),
      note TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Configuration table for app settings
  await runQuery(`
    CREATE TABLE IF NOT EXISTS app_config (
      id INTEGER PRIMARY KEY CHECK(id = 1),
      weekly_goal REAL DEFAULT 0.5 CHECK(weekly_goal >= 0 AND weekly_goal <= 5),
      threshold REAL DEFAULT 0.3 CHECK(threshold >= 0 AND threshold <= 2),
      start_week DATE,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Insert default configuration if not exists
  await runQuery(`
    INSERT OR IGNORE INTO app_config (id, weekly_goal, threshold) 
    VALUES (1, 0.5, 0.3)
  `);

  // Create indexes for better performance
  await runQuery(`
    CREATE INDEX IF NOT EXISTS idx_weight_entries_created_at 
    ON weight_entries(created_at)
  `);

  console.log('Database tables created successfully');
}

/**
 * Execute a SQL query with promise wrapper
 */
function runQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.run(sql, params, function(err) {
      if (err) {
        console.error('SQL Error:', err);
        console.error('Query:', sql);
        console.error('Params:', params);
        reject(err);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

/**
 * Execute a SELECT query
 */
function getQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.get(sql, params, (err, row) => {
      if (err) {
        console.error('SQL Error:', err);
        console.error('Query:', sql);
        console.error('Params:', params);
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

/**
 * Execute a SELECT query that returns multiple rows
 */
function allQuery(sql, params = []) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('Database not initialized'));
      return;
    }

    db.all(sql, params, (err, rows) => {
      if (err) {
        console.error('SQL Error:', err);
        console.error('Query:', sql);
        console.error('Params:', params);
        reject(err);
      } else {
        resolve(rows || []);
      }
    });
  });
}

/**
 * Close database connection
 */
function closeDatabase() {
  return new Promise((resolve, reject) => {
    if (!db) {
      resolve();
      return;
    }

    db.close((err) => {
      if (err) {
        reject(err);
      } else {
        db = null;
        console.log('Database connection closed');
        resolve();
      }
    });
  });
}

module.exports = {
  initializeDatabase,
  getDatabase,
  runQuery,
  getQuery,
  allQuery,
  closeDatabase
};
