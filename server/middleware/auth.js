const bcrypt = require('bcrypt');

/**
 * Authentication middleware for single-user setup
 * Supports Basic Auth with credentials from environment variables
 */
function authMiddleware(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader) {
    return res.status(401).json({ 
      error: 'Authorization header required',
      message: 'Please provide Basic Auth credentials'
    });
  }

  // Parse Basic Auth header
  if (!authHeader.startsWith('Basic ')) {
    return res.status(401).json({ 
      error: 'Invalid authorization format',
      message: 'Only Basic Auth is supported'
    });
  }

  try {
    // Decode base64 credentials
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
    const [username, password] = credentials.split(':');

    if (!username || !password) {
      return res.status(401).json({ 
        error: 'Invalid credentials format',
        message: 'Username and password are required'
      });
    }

    // Get expected credentials from environment
    const expectedUsername = process.env.AUTH_USERNAME;
    const expectedPassword = process.env.AUTH_PASSWORD;

    if (!expectedUsername || !expectedPassword) {
      console.error('AUTH_USERNAME or AUTH_PASSWORD not set in environment');
      return res.status(500).json({ 
        error: 'Server configuration error',
        message: 'Authentication not properly configured'
      });
    }

    // Verify credentials
    if (username !== expectedUsername) {
      return res.status(401).json({ 
        error: 'Invalid credentials',
        message: 'Username or password is incorrect'
      });
    }

    // For password comparison, we'll do a simple string comparison for now
    // In production, you might want to hash the password in the .env file
    if (password !== expectedPassword) {
      return res.status(401).json({ 
        error: 'Invalid credentials',
        message: 'Username or password is incorrect'
      });
    }

    // Authentication successful
    req.user = { username };
    next();

  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({ 
      error: 'Authentication failed',
      message: 'Invalid authorization header format'
    });
  }
}

module.exports = authMiddleware;
