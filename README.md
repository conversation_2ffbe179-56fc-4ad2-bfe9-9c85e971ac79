# Weight Tracker App

A minimal, elegant weight tracking app for a single user. Track your weight daily, analyze weekly trends, and stay on target with your diet goals.

## Features

- **Daily Weight Entry**: Log your weight with optional notes
- **Daily Weight Chart**: Line chart showing weight progression over time
- **Weekly Summary Chart**: Min/max/median weight per week with goal threshold lines
- **Weekly Deviation Chart**: Bar chart showing progress vs. predicted goals
- **Goal Configuration**: Set weekly weight loss goals and thresholds
- **Start Week Selector**: Define when your weight loss plan began

## Tech Stack

- **Frontend**: React + Vite + Tailwind CSS
- **Backend**: Node.js + Express
- **Database**: SQLite
- **Authentication**: Single-user basic auth via .env

## Quick Start

1. **Clone and install dependencies:**
   ```bash
   npm run install:all
   ```

2. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your credentials if needed
   ```

3. **Start development servers:**
   ```bash
   npm run dev
   ```

4. **Access the app:**
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001

5. **Login with default credentials:**
   - Username: `admin`
   - Password: `weight_tracker_2024`

6. **Test the setup:**
   ```bash
   ./test-api.sh
   ```

## Project Structure

```
weight-tracker/
├── client/          # React frontend
├── server/          # Express backend
├── data/           # SQLite database (auto-created)
├── package.json    # Root package.json for scripts
└── README.md
```

## Authentication

This is a single-user app. Set your credentials in the `.env` file:

```env
AUTH_USERNAME=your_username
AUTH_PASSWORD=your_secure_password
```

## API Endpoints

All endpoints require authentication via Basic Auth header.

- `POST /api/weight` - Add new weight entry
- `GET /api/weights/daily` - Get all daily entries
- `GET /api/weights/weekly` - Get weekly summaries
- `GET /api/weights/deviation` - Get weekly deviation from goals
- `POST /api/config` - Save goal and threshold settings
- `GET /api/config` - Get current configuration
- `POST /api/start-week` - Set/reset the start week

## Usage Guide

### Adding Weight Entries
1. Navigate to the "Add Weight" tab
2. Enter your weight in kg (e.g., 75.5)
3. Optionally add a note (e.g., "Morning weight")
4. Click "Add Weight Entry"

### Viewing Charts
- **Daily Weight Chart**: Shows your weight progression over time
- **Weekly Summary Chart**: Displays min/max/median weight per week with goal threshold lines
- **Weekly Deviation Chart**: Shows how you're performing vs. your weekly goals

### Configuring Goals
1. Go to the "Settings" tab
2. Set your weekly weight loss goal (kg/week)
3. Set your threshold (acceptable deviation in ±kg)
4. Set your start week (when you began your weight loss plan)
5. Click "Save Configuration"

### Understanding the Charts
- **Green bars/lines**: Better than expected progress
- **Red bars/lines**: Behind target (but keep going!)
- **Silver dashed lines**: Goal threshold boundaries
- **Blue line**: Your actual median weight progression

## Development

- `npm run dev` - Start both frontend and backend in development mode
- `npm run client:dev` - Start only frontend
- `npm run server:dev` - Start only backend
- `npm run build` - Build frontend for production
- `npm start` - Start production server

## Testing

Run the test script to verify all functionality:
```bash
./test-api.sh
```

## Troubleshooting

### Backend won't start
- Check if port 3001 is available
- Verify .env file exists and has correct credentials
- Check server logs for database errors

### Frontend won't load
- Check if port 5173 is available
- Verify backend is running on port 3001
- Check browser console for errors

### Authentication issues
- Verify credentials in .env file match what you're using
- Check if AUTH_USERNAME and AUTH_PASSWORD are set correctly

### Database issues
- Database file is created automatically in `./data/`
- Delete `./data/weight_tracker.db` to reset all data
- Check file permissions on the data directory

## License

MIT
